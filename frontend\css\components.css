/* 内容区域 */
.content-area {
    flex: 1;
    padding: var(--spacing-6);
    overflow-y: auto;
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-6);
    padding: var(--spacing-4);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
}

.breadcrumb-nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--gray-600);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: var(--transition);
}

.breadcrumb-item:hover {
    color: var(--primary-color);
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin-left: var(--spacing-2);
    color: var(--gray-400);
}

.view-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.view-toggle {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.view-btn {
    padding: var(--spacing-2) var(--spacing-3);
    background-color: var(--bg-primary);
    border: none;
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
}

.view-btn:hover {
    background-color: var(--gray-100);
}

.view-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.sort-controls select {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-primary);
    color: var(--gray-700);
    font-size: var(--font-size-sm);
    cursor: pointer;
}

/* 文件网格视图 */
.file-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
    padding: var(--spacing-2);
}

.file-item {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: var(--spacing-4);
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    min-height: 160px;
}

.file-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
    transform: translateY(-4px) scale(1.02);
    background: linear-gradient(145deg, #ffffff, #f0f8ff);
}

.file-item.selected {
    border-color: var(--primary-color);
    background: linear-gradient(145deg, #e3f2fd, #bbdefb);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
}

/* 文件夹特殊样式 */
.file-item.folder-item {
    background: linear-gradient(145deg, #fff8e1, #ffecb3);
    border-color: #ffc107;
}

.file-item.folder-item:hover {
    background: linear-gradient(145deg, #fff3c4, #ffe082);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.2);
    border-color: #ff8f00;
}

.file-icon {
    width: 80px;
    height: 80px;
    margin-bottom: var(--spacing-3);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.file-item:hover .file-icon {
    transform: scale(1.05);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 文件夹图标特殊样式 */
.file-item.folder-item .file-icon {
    background: linear-gradient(145deg, #ffd54f, #ffb300);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.file-item.folder-item:hover .file-icon {
    background: linear-gradient(145deg, #ffca28, #ff8f00);
    box-shadow: 0 6px 16px rgba(255, 193, 7, 0.4);
}

.file-icon i {
    font-size: 2.5rem;
    color: var(--gray-600);
    transition: all 0.3s ease;
}

.file-item.folder-item .file-icon i {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.file-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    transition: all 0.3s ease;
    opacity: 0;
    animation: fadeIn 0.5s ease forwards;
}

.file-item:hover .file-icon img {
    transform: scale(1.05);
}

/* 缩略图加载动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 加载占位符 */
.file-icon.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* 文件夹特殊动画效果 */
.file-item.folder-item .file-icon {
    position: relative;
    overflow: visible;
}

.file-item.folder-item .file-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ffd54f, #ffb300, #ff8f00, #ffd54f);
    background-size: 400% 400%;
    border-radius: 14px;
    z-index: -1;
    opacity: 0;
    animation: gradientShift 3s ease infinite;
    transition: opacity 0.3s ease;
}

.file-item.folder-item:hover .file-icon::before {
    opacity: 0.7;
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.file-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-1);
    word-break: break-word;
    line-height: 1.4;
    max-height: 2.8em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    transition: color 0.3s ease;
    text-align: center;
    width: 100%;
    flex-shrink: 0;
}

.file-item:hover .file-name {
    color: var(--primary-color);
}

.file-meta {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    background: rgba(0, 0, 0, 0.02);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: 8px;
    margin-top: auto;
    line-height: 1.3;
    text-align: center;
    width: 100%;
    flex-shrink: 0;
}

.file-size {
    display: block;
    font-weight: 500;
    color: var(--gray-600);
    text-align: center;
}

.file-actions {
    position: absolute;
    top: var(--spacing-2);
    right: var(--spacing-2);
    opacity: 0;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    z-index: 10;
}

.file-item:hover .file-actions {
    opacity: 1;
    transform: translateY(-2px);
}

.action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: var(--gray-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: linear-gradient(145deg, var(--primary-color), #0056b3);
    color: white;
    border-color: var(--primary-color);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.action-btn:active {
    transform: scale(0.95);
}

/* 文件列表视图 */
.file-list {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.file-table {
    width: 100%;
    border-collapse: collapse;
}

.file-table th {
    background-color: var(--bg-secondary);
    padding: var(--spacing-3) var(--spacing-4);
    text-align: left;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-700);
    border-bottom: 1px solid var(--border-color);
}

.file-table td {
    padding: var(--spacing-3) var(--spacing-4);
    border-bottom: 1px solid var(--border-color);
    font-size: var(--font-size-sm);
}

.file-table tr:hover {
    background-color: var(--gray-50);
}

.file-table tr.selected {
    background-color: var(--primary-light);
}

.file-table .file-name-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.file-table .file-icon {
    width: 32px;
    height: 32px;
    margin: 0;
}

.file-table .file-icon i {
    font-size: 1.2rem;
}

.file-table .file-actions {
    position: static;
    opacity: 1;
    display: flex;
    gap: var(--spacing-1);
}

.file-table .action-btn {
    width: 28px;
    height: 28px;
    margin: 0;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: var(--transition);
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-content.large {
    max-width: 800px;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
}

.modal-close {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    transition: var(--transition);
}

.modal-close:hover {
    background-color: var(--gray-100);
    color: var(--gray-700);
}

.modal-body {
    padding: var(--spacing-6);
    max-height: 60vh;
    overflow-y: auto;
}

/* 上传区域 */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-8);
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.upload-area i {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: var(--spacing-4);
}

.upload-area p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-4);
}

.upload-progress {
    margin-top: var(--spacing-4);
}

.progress-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-2);
}

.progress-info {
    flex: 1;
}

.progress-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-800);
}

.progress-status {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.progress-bar {
    width: 100px;
    height: 6px;
    background-color: var(--gray-200);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

/* 通知面板 */
.notification-panel {
    position: fixed;
    top: 64px;
    right: 0;
    width: 400px;
    height: calc(100vh - 64px);
    background-color: var(--bg-primary);
    border-left: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    z-index: 1500;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
}

.notification-panel.show {
    transform: translateX(0);
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
}

.close-panel {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    transition: var(--transition);
}

.close-panel:hover {
    background-color: var(--gray-100);
    color: var(--gray-700);
}

.notification-list {
    padding: var(--spacing-4);
    height: calc(100% - 80px);
    overflow-y: auto;
}

.notification-item {
    padding: var(--spacing-4);
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-3);
    border-left: 4px solid var(--primary-color);
}

.notification-item.warning {
    border-left-color: var(--warning-color);
}

.notification-item.error {
    border-left-color: var(--error-color);
}

.notification-item.success {
    border-left-color: var(--success-color);
}

.notification-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-1);
}

.notification-message {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-2);
}

.notification-time {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* 右键菜单 */
.context-menu {
    position: fixed;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    z-index: 3000;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.9);
    transition: var(--transition-fast);
}

.context-menu.show {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.context-menu ul {
    list-style: none;
    margin: 0;
    padding: var(--spacing-2) 0;
}

.context-menu li {
    padding: var(--spacing-2) var(--spacing-4);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    transition: var(--transition);
}

.context-menu li:hover {
    background-color: var(--gray-100);
}

.context-menu li.divider {
    height: 1px;
    background-color: var(--border-color);
    margin: var(--spacing-2) 0;
    padding: 0;
    cursor: default;
}

.context-menu li.divider:hover {
    background-color: var(--border-color);
}

.context-menu i {
    width: 16px;
    text-align: center;
}

/* Toast 通知 */
.toast-container {
    position: fixed;
    top: 80px;
    right: var(--spacing-6);
    z-index: 4000;
}

.toast {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-3);
    min-width: 300px;
    max-width: 400px;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.info {
    border-left: 4px solid var(--primary-color);
}

.toast-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-2);
}

.toast-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-800);
}

.toast-close {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    font-size: var(--font-size-sm);
}

.toast-message {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

/* 预览容器 */
.preview-container {
    text-align: center;
    max-height: 60vh;
    overflow: auto;
}

.preview-container img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius);
}

.preview-container video {
    max-width: 100%;
    height: auto;
}

.preview-container iframe {
    width: 100%;
    height: 500px;
    border: none;
    border-radius: var(--border-radius);
}

.preview-placeholder {
    padding: var(--spacing-8);
    color: var(--gray-500);
}

.preview-placeholder i {
    font-size: 4rem;
    margin-bottom: var(--spacing-4);
    color: var(--gray-400);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .nav-search {
        max-width: 400px;
        margin: 0 var(--spacing-4);
    }
    
    .sidebar {
        width: 240px;
    }
    
    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: var(--spacing-3);
    }

    .file-grid.extra-large-icons {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }

    .file-grid.large-icons {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }

    .file-grid.medium-icons {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .file-grid.small-icons {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 var(--spacing-4);
    }
    
    .nav-search {
        display: none;
    }
    
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }
    
    .content-area {
        padding: var(--spacing-4);
    }
    
    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: var(--spacing-2);
    }

    .file-grid.extra-large-icons {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    }

    .file-grid.large-icons {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }

    .file-grid.medium-icons {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }

    .file-grid.small-icons {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }

    .file-item {
        padding: var(--spacing-3);
    }

    .file-icon {
        width: 60px;
        height: 60px;
    }

    .file-icon i {
        font-size: 2rem;
    }

    .file-name {
        font-size: var(--font-size-xs);
    }

    .file-actions {
        opacity: 1;
        position: static;
        flex-direction: row;
        justify-content: center;
        margin-top: var(--spacing-2);
    }

    .action-btn {
        width: 28px;
        height: 28px;
        margin: 0 2px;
    }
    
    .breadcrumb {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: flex-start;
    }
    
    .view-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .notification-panel {
        width: 100%;
    }
    
    .modal-content {
        width: 95%;
        margin: var(--spacing-4);
    }
}

/* 预览模态框增强样式 */
.modal-content.large {
    width: 90vw;
    max-width: 1200px;
    max-height: 90vh;
}

.modal-content.large .modal-body {
    max-height: calc(90vh - 120px);
    overflow: auto;
    padding: var(--spacing-4);
}

.preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    max-height: 80vh;
    overflow: auto;
    background: #f8f9fa;
    border-radius: 8px;
    position: relative;
}

.preview-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.preview-container img:hover {
    transform: scale(1.02);
}

.preview-container video {
    max-width: 100%;
    max-height: 100%;
    border-radius: 4px;
}

/* 加载动画 */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 预览占位符增强 */
.preview-placeholder {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.preview-placeholder i {
    font-size: 64px;
    margin-bottom: 20px;
    color: #ddd;
}

.preview-placeholder p {
    font-size: 18px;
    margin-bottom: 20px;
}
