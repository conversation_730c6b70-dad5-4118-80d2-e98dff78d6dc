/* 全局样式 */
:root {
    /* 主色调 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;
    
    /* 辅助色 */
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* 中性色 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-hover: #f1f5f9;

    /* 文本颜色 */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    
    /* 边框 */
    --border-color: #e2e8f0;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* 字体 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* 间距 */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    
    /* 过渡 */
    --transition: all 0.2s ease-in-out;
    --transition-fast: all 0.15s ease-in-out;
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-secondary);
    color: var(--gray-800);
    overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 4px;
    transition: var(--transition);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 通用类 */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-sm);
    font-weight: 500;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--gray-100);
    color: var(--gray-700);
    border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--gray-200);
}

.btn-ghost {
    background-color: transparent;
    color: var(--gray-600);
}

.btn-ghost:hover:not(:disabled) {
    background-color: var(--gray-100);
    color: var(--gray-800);
}

.btn-sm {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
}

/* 输入框样式 */
.input {
    width: 100%;
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-primary);
    transition: var(--transition);
}

.input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.input::placeholder {
    color: var(--gray-400);
}

/* 卡片样式 */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

.card-body {
    padding: var(--spacing-6);
}

.card-footer {
    padding: var(--spacing-4) var(--spacing-6);
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.3s ease-in-out;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-4);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 导航栏 */
.navbar {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    width: 100%;
    padding: 0 var(--spacing-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-800);
    flex-shrink: 0;
    min-width: 180px;
}

.nav-brand i {
    font-size: var(--font-size-xl);
    color: var(--primary-color);
}

.nav-search {
    flex: 1;
    max-width: 500px;
    margin: 0 var(--spacing-6);
}

.search-container {
    position: relative;
}

.search-container i {
    position: absolute;
    left: var(--spacing-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
}

.search-container input {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-3) var(--spacing-3) var(--spacing-10);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    background-color: var(--bg-secondary);
    transition: var(--transition);
}

.search-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: var(--bg-primary);
    box-shadow: var(--shadow-md);
}

.search-filters {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    display: flex;
    gap: var(--spacing-2);
    margin-top: var(--spacing-1);
    padding: var(--spacing-2);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 100;
}

.search-container:focus-within .search-filters,
.search-filters:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-xs);
    background-color: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn:hover {
    background-color: var(--gray-100);
}

.filter-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    flex-shrink: 0;
    min-width: 200px;
}

.nav-btn {
    position: relative;
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    background-color: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--gray-700);
    cursor: pointer;
    transition: var(--transition);
}

.nav-btn:hover {
    background-color: var(--gray-100);
}

.nav-btn i {
    font-size: var(--font-size-sm);
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background-color: var(--error-color);
    color: white;
    font-size: var(--font-size-xs);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-menu {
    position: relative;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.user-avatar:hover {
    background-color: var(--primary-hover);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--spacing-2);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-info {
    padding: var(--spacing-4);
}

.user-name {
    font-weight: 600;
    color: var(--gray-800);
}

.user-role {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--gray-100);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    min-height: calc(100vh - 64px);
}

/* 侧边栏 */
.sidebar {
    width: 280px;
    background-color: var(--bg-primary);
    border-right: 1px solid var(--border-color);
    padding: var(--spacing-6);
    overflow-y: auto;
    flex-shrink: 0;
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: var(--spacing-6);
    overflow-y: auto;
    background-color: var(--bg-secondary);
}

.sidebar-section {
    margin-bottom: var(--spacing-8);
}

.sidebar-section h3 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--spacing-4);
}

.sidebar-menu {
    list-style: none;
}

.menu-item {
    margin-bottom: var(--spacing-1);
}

.menu-item a {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.menu-item a:hover {
    background-color: var(--gray-100);
}

.menu-item.active a {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.menu-item i {
    width: 16px;
    text-align: center;
}

.folder-list {
    list-style: none;
}

.folder-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    color: var(--gray-700);
    cursor: pointer;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.folder-item:hover {
    background-color: var(--gray-100);
}

.folder-item i {
    color: var(--warning-color);
}

.storage-info {
    padding: var(--spacing-4);
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.storage-bar {
    width: 100%;
    height: 8px;
    background-color: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-2);
}

.storage-used {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
    transition: width 0.3s ease;
}

.storage-text {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-xs);
    color: var(--gray-600);
}

/* 系统状态样式已移除 */

/* 用户菜单样式 */
.user-menu {
    position: relative;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown .user-info {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.user-dropdown .user-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.user-dropdown .user-role {
    font-size: 12px;
    color: var(--text-secondary);
}

.user-dropdown .dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.user-dropdown .dropdown-item:hover {
    background: var(--bg-hover);
}

.user-dropdown .dropdown-item i {
    width: 16px;
    text-align: center;
    color: var(--text-secondary);
}

/* 通知面板样式 */
.notification-panel {
    position: fixed;
    top: 60px;
    right: 20px;
    width: 350px;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.notification-panel.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.close-panel {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 4px;
}

.close-panel:hover {
    color: var(--text-primary);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-content.large {
    width: 80vw;
    height: 80vh;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

/* Toast通知样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 300px;
    max-width: 400px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--error-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

.toast-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px 8px;
}

.toast-title {
    font-weight: 600;
    font-size: 14px;
}

.toast-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 2px;
}

.toast-message {
    padding: 0 16px 12px;
    font-size: 14px;
    color: var(--text-secondary);
}

/* 右键菜单样式 */
.context-menu {
    position: fixed;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.95);
    transition: all 0.2s ease;
}

.context-menu.show {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.context-menu ul {
    list-style: none;
    margin: 0;
    padding: 4px 0;
}

.context-menu li {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 14px;
}

.context-menu li:hover {
    background: var(--bg-hover);
}

.context-menu li.divider {
    height: 1px;
    background: var(--border-color);
    margin: 4px 0;
    padding: 0;
}

.context-menu li i {
    width: 16px;
    text-align: center;
    color: var(--text-secondary);
}

/* 错误页面样式 */
.error-page {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: var(--bg-secondary);
}

.error-content {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-width: 500px;
}

.error-content i {
    font-size: 64px;
    color: var(--error-color);
    margin-bottom: 20px;
}

.error-content h1 {
    font-size: 24px;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.error-content p {
    color: var(--text-secondary);
    margin-bottom: 24px;
    line-height: 1.6;
}

/* 文件缩略图样式 */
.file-thumbnail {
    position: relative;
    width: 100%;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.file-thumbnail:hover img {
    transform: scale(1.05);
}

.file-icon-fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 48px;
    color: var(--text-secondary);
}

/* 文件网格项样式优化 */
.file-item {
    position: relative;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 16px;
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
}

.file-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.file-item.selected {
    border-color: var(--primary-color);
    background: var(--primary-color-light);
    box-shadow: 0 0 0 2px var(--primary-color-alpha);
}

.file-item .file-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 120px;
    margin-bottom: 12px;
    font-size: 48px;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    border-radius: 8px;
}

.file-item .file-name {
    font-weight: 500;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--text-primary);
}

.file-item .file-meta {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 12px;
}

.file-item .file-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.file-item:hover .file-actions {
    opacity: 1;
}

.file-item .action-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
    font-size: 14px;
}

.file-item .action-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* 文件列表样式 */
.file-list-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-list-table th {
    background: var(--bg-secondary);
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

.file-list-table td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.file-list-table tr:hover {
    background: var(--bg-hover);
}

.file-list-table tr.selected {
    background: var(--primary-color-light);
}

.file-name-cell {
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-name-cell .file-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--text-secondary);
}

/* 预览模态框样式 */
.preview-modal .modal-content {
    max-width: 90vw;
    max-height: 90vh;
}

.preview-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background: var(--bg-secondary);
    border-radius: 8px;
    overflow: hidden;
}

.preview-container img {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
}

.preview-container video {
    max-width: 100%;
    max-height: 70vh;
}

.preview-placeholder {
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);
}

.preview-placeholder i {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.preview-placeholder p {
    margin-bottom: 20px;
    font-size: 16px;
}

/* 加载动画 */
.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式导航栏 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 var(--spacing-4);
    }

    .nav-brand {
        min-width: 120px;
        font-size: var(--font-size-base);
    }

    .nav-search {
        max-width: 300px;
        margin: 0 var(--spacing-4);
    }

    .nav-actions {
        min-width: 150px;
        gap: var(--spacing-2);
    }

    .nav-btn span {
        display: none;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 var(--spacing-2);
    }

    .nav-brand span {
        display: none;
    }

    .nav-search {
        max-width: 200px;
        margin: 0 var(--spacing-2);
    }

    .search-filters {
        display: none;
    }
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-4);
    border-bottom: 1px solid var(--border-color);
}

.breadcrumb-nav {
    display: flex;
    align-items: center;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--text-secondary);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color 0.2s ease;
}

.breadcrumb-item:hover {
    color: var(--primary-color);
}

.breadcrumb-item i {
    font-size: 14px;
}

/* 视图控制 */
.view-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.view-toggle {
    display: flex;
    background: var(--bg-primary);
    border-radius: 6px;
    padding: 2px;
}

.view-btn {
    background: none;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.view-btn.active,
.view-btn:hover {
    background: var(--primary-color);
    color: white;
}

.sort-controls select {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    cursor: pointer;
}

/* 文件网格 - 基础样式 */
.file-grid {
    display: grid;
    gap: var(--spacing-4);
    padding: var(--spacing-4) 0;
}

/* 超大图标视图 */
.file-grid.extra-large-icons {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

.file-grid.extra-large-icons .file-item .file-icon {
    height: 180px;
    font-size: 80px;
}

.file-grid.extra-large-icons .file-item .file-icon img {
    max-width: 160px;
    max-height: 160px;
}

/* 大图标视图 */
.file-grid.large-icons {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
}

.file-grid.large-icons .file-item .file-icon {
    height: 140px;
    font-size: 64px;
}

.file-grid.large-icons .file-item .file-icon img {
    max-width: 120px;
    max-height: 120px;
}

/* 中等图标视图 */
.file-grid.medium-icons {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
}

.file-grid.medium-icons .file-item .file-icon {
    height: 100px;
    font-size: 48px;
}

.file-grid.medium-icons .file-item .file-icon img {
    max-width: 80px;
    max-height: 80px;
}

.file-grid.medium-icons .file-item {
    padding: 12px;
}

/* 小图标视图 */
.file-grid.small-icons {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
}

.file-grid.small-icons .file-item .file-icon {
    height: 60px;
    font-size: 32px;
}

.file-grid.small-icons .file-item .file-icon img {
    max-width: 48px;
    max-height: 48px;
}

.file-grid.small-icons .file-item {
    padding: 8px;
}

.file-grid.small-icons .file-item .file-meta {
    display: none;
}

/* 文件夹特殊样式 */
.file-item.folder-item {
    border-color: var(--warning-color);
    background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(255, 193, 7, 0.1) 100%);
}

.file-item.folder-item:hover {
    border-color: var(--warning-color);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
    transform: translateY(-2px);
}

.file-item.folder-item .file-icon {
    color: var(--warning-color);
}

/* 首页文件夹样式 */
.file-grid:not(.large-icons):not(.medium-icons):not(.small-icons):not(.extra-large-icons) {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
}

/* 图片文件缩略图样式 */
.file-item .file-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 面包屑分隔符 */
.breadcrumb-separator {
    margin: 0 8px;
    color: var(--text-secondary);
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: 500;
}

.file-item {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--spacing-4);
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.file-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.file-icon {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: var(--spacing-2);
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-1);
    word-break: break-word;
}

.file-info {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* 文件列表 */
.file-list {
    background: var(--bg-primary);
    border-radius: 8px;
    overflow: hidden;
}

.file-table {
    width: 100%;
    border-collapse: collapse;
}

.file-table th,
.file-table td {
    padding: var(--spacing-3) var(--spacing-4);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.file-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.file-table tr:hover {
    background: var(--bg-hover);
}

/* 隐藏类 */
.hidden {
    display: none !important;
}
