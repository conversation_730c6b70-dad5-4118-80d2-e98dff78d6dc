2025-06-08 00:01:18 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:08:16 - APIServer - INFO - API服务器已停止
2025-06-08 00:08:23 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:17:45 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:17:45 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 00:17:51 - APIServer - INFO - API服务器已停止
2025-06-08 00:17:53 - APIServer - INFO - API服务器已停止
2025-06-08 00:17:54 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:19:13 - APIServer - INFO - API服务器已停止
2025-06-08 00:22:25 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:32:46 - APIServer - INFO - API服务器已停止
2025-06-08 00:32:53 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:36:26 - APIServer - ERROR - 获取文件列表失败: 'FileService' object has no attribute 'get_root_files'
2025-06-08 00:36:29 - APIServer - ERROR - 获取文件列表失败: 'FileService' object has no attribute 'get_root_files'
2025-06-08 00:36:30 - APIServer - ERROR - 获取文件列表失败: 'FileService' object has no attribute 'get_root_files'
2025-06-08 00:37:27 - APIServer - INFO - API服务器已停止
2025-06-08 00:37:33 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:37:43 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:37:43 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:38:49 - APIServer - INFO - API服务器已停止
2025-06-08 00:46:53 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:47:21 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:21 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:34 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:34 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:35 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:47:35 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:48:00 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:48:00 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:50:20 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:50:30 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:50:30 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:53:17 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:55:35 - APIServer - INFO - API服务器已停止
2025-06-08 00:55:44 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 00:58:19 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:58:19 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:59:26 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 00:59:26 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:01:37 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:01:38 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:03:59 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:03:59 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:04:00 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:04:00 - APIServer - ERROR - 获取文件列表失败: FileService.get_folder_files() takes from 2 to 4 positional arguments but 5 were given
2025-06-08 01:08:18 - APIServer - INFO - 加密服务初始化成功
2025-06-08 01:08:19 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 01:09:14 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 01:09:14 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 01:09:58 - APIServer - INFO - API服务器已停止
2025-06-08 01:10:05 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 01:17:04 - APIServer - INFO - 加密服务初始化成功
2025-06-08 01:17:04 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 01:17:04 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 01:18:20 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 01:22:17 - APIServer - INFO - 加密服务初始化成功
2025-06-08 01:22:18 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 01:38:50 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 01:44:26 - APIServer - INFO - API服务器已停止
2025-06-08 01:44:36 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 01:51:07 - APIServer - INFO - API服务器已停止
2025-06-08 01:51:30 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 01:52:01 - APIServer - INFO - API服务器已停止
2025-06-08 01:54:13 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 01:55:58 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 01:55:58 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 01:56:15 - APIServer - INFO - API服务器已停止
2025-06-08 01:56:18 - APIServer - INFO - API服务器已停止
2025-06-08 01:56:18 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 01:57:22 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 02:14:57 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 02:14:57 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 02:17:20 - APIServer - INFO - API服务器已停止
2025-06-08 02:17:26 - APIServer - INFO - API服务器已停止
2025-06-08 02:24:16 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 02:29:11 - APIServer - INFO - API服务器已停止
2025-06-08 02:29:17 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 02:31:01 - APIServer - INFO - API服务器已停止
2025-06-08 02:46:54 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:02:24 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:08:02 - APIServer - INFO - API服务器已停止
2025-06-08 17:09:23 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:17:15 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:21:30 - APIServer - INFO - API服务器已停止
2025-06-08 17:21:36 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:24:15 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:24:15 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 17:24:25 - APIServer - INFO - API服务器已停止
2025-06-08 17:24:26 - APIServer - INFO - API服务器已停止
2025-06-08 17:24:27 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:24:53 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:28:17 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:28:17 - APIServer - ERROR - API服务器运行失败: [WinError 10048] 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
2025-06-08 17:28:52 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:29:08 - APIServer - ERROR - 下载文件失败: 'full_path'
2025-06-08 17:29:08 - APIServer - ERROR - 下载文件失败: 'full_path'
2025-06-08 17:29:20 - APIServer - ERROR - 下载文件失败: 'full_path'
2025-06-08 17:29:20 - APIServer - ERROR - 下载文件失败: 'full_path'
2025-06-08 17:30:15 - APIServer - INFO - API服务器已停止
2025-06-08 17:34:23 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:40:21 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:42:19 - APIServer - INFO - API服务器已停止
2025-06-08 17:42:31 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:44:19 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 17:45:57 - APIServer - ERROR - 获取缩略图失败: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-08 17:48:00 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
2025-06-08 18:18:52 - APIServer - INFO - API服务器启动: 0.0.0.0:8086
